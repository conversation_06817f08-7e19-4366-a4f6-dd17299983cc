'use client'

import {
  useMemo, useState,
} from 'react';
import Image from 'next/image';
import { cn } from '@/common/utils/helpers';
import { PlusIcon } from '@/common/components/icons';
import { Button } from '@/common/components/atoms';
import { secondaryFont } from '@/common/utils/localFont';

interface CalendarDayEvent {
  id: string;
  title: string;
  platform: string;
  scheduledTime: Date;
  status: string;
  attachments?: string[];
}

interface CalendarDayProps {
  date: Date | null;
  isCurrentMonth: boolean;
  isToday: boolean;
  isPastDate: boolean;
  events?: CalendarDayEvent[];
  onDateClick?: (date: Date) => void;
  onViewPosts?: (date: Date) => void;
}

export const CalendarDay = ({
  date, isCurrentMonth, isToday, isPastDate, events = [], onDateClick, onViewPosts,
}: CalendarDayProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const dayNumber = useMemo(() => date ? date.getDate() : '', [date]);

  const backgroundImage = useMemo(() => {
    if (events?.length && events[0].attachments) {
    }
    return events.find(event => event.attachments?.[0])?.attachments?.[0];
  }, [events]);

  if (!date) {
    return <div className="h-24 border border-white/5 bg-violets-are-blue/5 rounded-lg"></div>;
  }

  const handleClick = () => {
    if (date && onDateClick && !isPastDate) {
      const clickedDate = new Date(date);
      onDateClick(clickedDate);
    }
  };

  const handleViewPosts = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (date && onViewPosts) {
      onViewPosts(date);
    }
  };
  return (
    <div
      className={cn(
        "h-24 p-2 border border-white/5 rounded-lg transition-all relative group flex flex-col justify-between overflow-hidden",
        isCurrentMonth ? "bg-violets-are-blue/15" : "bg-violets-are-blue/10 opacity-50",
        isToday ? "ring-1 ring-violets-are-blue" : "",
        isPastDate ? "opacity-60 bg-gray-800/30" : "",
        !isPastDate ? "cursor-pointer hover:border-violets-are-blue/20 hover:bg-violets-are-blue/10 hover:opacity-100" : "",
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {backgroundImage && (
        <div className="absolute inset-0 z-0">
          <Image
            src={backgroundImage}
            alt=""
            fill
            className="object-cover opacity-20"
            quality={25}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
        </div>
      )}

      <div className="relative z-10 flex flex-col h-full">
        <div className="flex justify-between items-start">
          <span
            className={cn(
              `text-sm font-semibold ${secondaryFont.className}`,
              isCurrentMonth ? (isPastDate ? "text-gray-400" : "text-white") : "text-gray-500",
              isToday ? "text-violets-are-blue" : "",
            )}
          >
            {dayNumber}
          </span>

          {events.length > 0 && isHovered && !isPastDate && (
            <div className={cn(
              "transition-opacity duration-200",
              isHovered ? "opacity-100" : "opacity-0",
              "group-hover:opacity-100",
            )}>
              <div
                className="bg-violets-are-blue/20 hover:bg-violets-are-blue/30 rounded-full p-1 cursor-pointer"
                onClick={handleClick}
              >
                <PlusIcon className="w-3 h-3 text-white" />
              </div>
            </div>
          )}
        </div>

        <div className="mt-auto">
          {events.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="text-xs text-violets-are-blue font-medium">
                {events.length} {events.length === 1 ? 'post' : 'posts'}
              </div>
              {events.length > 0 && isHovered ? (
                <Button
                  variant="outline"
                  size="xs"
                  className="backdrop-blur-sm"
                  onClick={handleViewPosts}
                >
                  View
                </Button>
              ) : <div className='h-[24px]'></div>}
            </div>
          )}
        </div>
      </div>

      {events.length === 0 && isHovered && !isPastDate && (
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div
            className="bg-violets-are-blue/20 hover:bg-violets-are-blue/30 rounded-full p-2 cursor-pointer transition-all"
            onClick={handleClick}
          >
            <PlusIcon className="w-4 h-4 text-white" />
          </div>
        </div>
      )}
    </div>
  );
};
