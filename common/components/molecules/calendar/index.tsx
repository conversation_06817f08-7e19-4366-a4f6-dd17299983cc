'use client'

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { CircularSpinner } from '@/common/components/atoms';
import {
  usePosts,
} from '@/common/hooks/usePosts';
import { routes } from '@/common/routes';
import { CalendarHeader } from './calendarHeader';
import { CalendarGrid } from './calendarGrid';
import { ViewPostsModal } from './viewPostsModal';

export const Calendar = ({
  connectedAccounts,
}: {
  connectedAccounts: Array<{ agentId: string | null; platform: string; connected: boolean }>
}) => {
  const router = useRouter();
  const [currentDate] = useState(new Date());
  const [displayDate, setDisplayDate] = useState(new Date());

  const {
    scheduledPostsByDate,
    isLoading,
    viewPostsDate,
    isViewPostsModalOpen,
    viewPosts,
    setIsViewPostsModalOpen,
    handleViewPosts,
    refreshPosts,
  } = usePosts(connectedAccounts);

  const handlePrevMonth = () => {
    setDisplayDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() - 1);
      return newDate;
    });
  };

  const handleNextMonth = () => {
    setDisplayDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() + 1);
      return newDate;
    });
  };

  const handleDateClick = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dateParam = `${year}-${month}-${day}`;
    const url = `${routes.dashboardCalendarSchedulePath}?date=${dateParam}`;
    router.push(url);
  };
 

  return (
    <div className="w-full">
      <CalendarHeader
        displayDate={displayDate}
        onPrevMonth={handlePrevMonth}
        onNextMonth={handleNextMonth}
      />
      {isLoading ? (
        <div className="flex justify-center items-center h-full min-h-[400px] w-full">
          <CircularSpinner />
        </div>
      ) : (
        <CalendarGrid
          currentDate={currentDate}
          displayDate={displayDate}
          onDateClick={handleDateClick}
          onViewPosts={handleViewPosts}
          scheduledPosts={scheduledPostsByDate}
        />
      )}
      {viewPostsDate && (
        <ViewPostsModal
          isOpen={isViewPostsModalOpen}
          onClose={() => setIsViewPostsModalOpen(false)}
          date={viewPostsDate}
          handleViewPosts={handleViewPosts}
          posts={viewPosts}
          onPostUpdated={refreshPosts}
        />
      )}
    </div>
  );
};
