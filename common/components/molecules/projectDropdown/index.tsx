'use client'

import { 
  useState, useRef,
} from 'react'
import { 
  motion, AnimatePresence,
} from 'framer-motion'
import { useOutsideClick } from '@/common/hooks'
import { cn } from '@/common/utils/helpers'
import { Project } from '@/common/types/supabase'
import { 
  ChevronDownIcon, PlusIcon,
} from '@/common/components/icons'
import { secondaryFont } from '@/common/utils/localFont'

interface ProjectDropdownProps {
  projects: Project[]
  activeProject: Project | null
  onSelectProject: (projectId: string) => void
  onCreateProject: () => void
  collapsed: boolean
}

export const ProjectDropdown = ({ 
  projects,
  activeProject,
  onSelectProject,
  onCreateProject,
  collapsed,
}: ProjectDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useOutsideClick({
    ref: dropdownRef,
    callback: () => setIsOpen(false),
    isVisible: isOpen,
  })

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full focus:outline-none"
        aria-label="Project menu"
      >
        <div className="flex items-center gap-2">
          <div className={`h-8 w-8 rounded-lg flex items-center justify-center bg-gradient-to-tr from-han-purple to-tulip text-white font-medium ${secondaryFont.className}`}>
            {activeProject ? activeProject.name.charAt(0).toUpperCase() : 'P'}
          </div>
          {!collapsed && (
            <span className="text-white font-semibold truncate max-w-32">
              {activeProject ? activeProject.name : 'Select Project'}
            </span>
          )}
        </div>
        {!collapsed && (
          <ChevronDownIcon className={` ${isOpen ? 'rotate-180' : ''}`} />
        )}
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ 
              opacity: 0, 
              y: -10,
            }}
            animate={{ 
              opacity: 1, 
              y: 0,
            }}
            exit={{ 
              opacity: 0, 
              y: -10,
            }}
            transition={{ duration: 0.2 }}
            className={cn(
              "absolute left-0 right-0 mt-2 rounded-xl bg-eerie-black/90 backdrop-blur-md",
              "border border-white/10 shadow-lg z-50 overflow-hidden",
              collapsed ? "w-48" : "w-full",
            )}
          >
            <div className="py-2 px-1">
              <div className="max-h-60 overflow-y-auto space-y-1">
                {projects.length > 0 ? (
                  projects.map((project) => (
                    <button
                      key={project.project_id}
                      onClick={() => {
                        onSelectProject(project.project_id)
                        setIsOpen(false)
                      }}
                      className={cn(
                        "w-full text-left px-3 py-2 text-sm rounded-lg transition-colors duration-150",
                        activeProject?.project_id === project.project_id 
                          ? "bg-gradient-to-tr from-han-purple to-tulip text-white" 
                          : "text-gray-300 hover:text-white hover:bg-white/5",
                      )}
                    >
                      {project.name}
                    </button>
                  ))
                ) : (
                  <div className="px-3 py-2 text-sm text-gray-400">
                    No projects available
                  </div>
                )}
              </div>
              <div className="border-t border-white/10 mt-2 pt-2">
                <button
                  onClick={() => {
                    onCreateProject()
                    setIsOpen(false)
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-colors duration-150 flex items-center gap-2"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>Create New Project</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
