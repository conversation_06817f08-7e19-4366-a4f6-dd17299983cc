'use client'

import {
  useState,
  useRef,
  useEffect,
} from 'react';
import {
  useEditor,
  EditorContent,
} from '@tiptap/react'
import Image from 'next/image';
import Placeholder from '@tiptap/extension-placeholder'
import { 
  ImagePlus, 
  X,
} from 'lucide-react';
import { Color } from '@tiptap/extension-color'
import ListItem from '@tiptap/extension-list-item'
import TextStyle from '@tiptap/extension-text-style'
import StarterKit from '@tiptap/starter-kit'
import Highlight from '@tiptap/extension-highlight'
import { useOutsideClick } from "@/common/hooks/useOutsideClick";
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import BubbleMenus from './BubbleMenus';
import {
  CircularSpinner,
} from '../../atoms';
import {
  Instance, Props,
} from './types';
import './editor.css';

interface TipTapEditorProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (html: string) => void;
  onAIRequest?: (prompt: string, selectedText: string) => Promise<string>;
  onNewPostRequest: (topic: string, value: boolean, value2: boolean) => Promise<string>;
  onImageButtonClick?: () => void;
  attachedImages?: string[];
  handleRemoveImage: () => void;
  characterLimit?: number;
  platformName?: string;
}

export const TipTapEditor = ({
  initialContent = '',
  placeholder = 'Start typing...',
  onChange,
  onAIRequest,
  onNewPostRequest,
  onImageButtonClick,
  attachedImages = [],
  handleRemoveImage,
  characterLimit,
}: TipTapEditorProps) => {
  const [stateHighlightedText, setStateHighlightedText] = useState('')
  const [enableCleanUp, setEnableCleanUp] = useState(false)
  const [newPromptValue, setNewPromptValue] = useState('');
  const [isPromptRequesting, setIsPromptRequesting] = useState(false);
  const [showTopicInput, setShowTopicInput] = useState(false);
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [characterCount, setCharacterCount] = useState(0);
  const { trackContentEvent } = useMixpanelEvent();

  const aiEnhanceRef = useRef<Instance<Props>>()
  const cursorMenuRef = useRef<Instance<Props>>()
  const aiMenuRef = useRef<HTMLDivElement>(null);
  const cursorDivRef = useRef<HTMLDivElement>(null);

  useOutsideClick({
    isVisible: enableCleanUp,
    ref: aiMenuRef,
    callback: () => cleanMark(),
  })

  useOutsideClick({
    isVisible: showTopicInput,
    ref: cursorDivRef,
    callback: () => setShowTopicInput(false),
  })

  const editor = useEditor({
    extensions: [
      Highlight,
      Color.configure({ types: [TextStyle.name, ListItem.name] }),
      Placeholder.configure({
        placeholder,
      }),
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
      }),
    ],
    content: initialContent,
    editorProps: {
      attributes: {
        class: 'outline-none h-full w-full',
      },
      handleKeyDown: (_view, event) => {
        if (characterLimit && characterCount >= characterLimit) {
          const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Tab'];
          if (!allowedKeys.includes(event.key) && !event.ctrlKey && !event.metaKey) {
            event.preventDefault();
            return true;
          }
        }
        return false;
      },
    },
    onSelectionUpdate: ({ editor }) => {
      if (editor) {
        const {
          view, state,
        } = editor;
        const {
          from, to,
        } = view.state.selection
        const highlightedText = state.doc.textBetween(from, to, '\n')
        setStateHighlightedText(highlightedText)
      }
    },
    onUpdate: ({ editor }) => {
      const text = editor.getText();
      const currentLength = text.length;
      setCharacterCount(currentLength);

      if (onChange) {
        onChange(text);
      }
    },
    parseOptions: {
      preserveWhitespace: 'full',
    },
  })

  const cleanMark = () => {
    if (editor) {
      editor.commands.unsetHighlight();
      setEnableCleanUp(false);
    }
  }

  const handleWritePromptFocus = () => {
    if (stateHighlightedText && editor) {
      editor.commands.setHighlight();
      setEnableCleanUp(true);

      setTimeout(() => {
        const inputElement = document.getElementById('write-prompt') as HTMLInputElement;
        if (inputElement) {
          inputElement.focus();
        }
      }, 0);
    }
  }

  const handleKeyPress = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && newPromptValue && stateHighlightedText) {
      await handleEnhanceText(`Find this copy: %selectedText% in the whole paragraph/context and reframe only this bit to improve with below suggestion: /n## START OF SUGGESTION/n${newPromptValue}/n## END OF SUGGESTION/n`);
      setNewPromptValue('');
    }
  }

  const handleEnhanceText = async (prompt: string) => {
    if (!editor || !onAIRequest) {
      return;
    }
    try {
      setIsPromptRequesting(true);
      if (stateHighlightedText) {
        const result = await onAIRequest(`${prompt.replace('%selectedText%', `/n/n${stateHighlightedText}/n/n`)}`, editor.getText());
        if (editor && result) {
          editor.commands.clearContent();
          editor.commands.insertContent(result);
          cleanMark();
          if (onChange) {
            onChange(editor.getText());
          }

          trackContentEvent('enhance', {
            enhanceType: stateHighlightedText ? 'selected_text' : 'full_content',
            contentLength: result.length,
          });
        }
        aiEnhanceRef?.current?.hide();
        cursorMenuRef?.current?.hide();

      } else {
        const result = await onAIRequest(prompt, editor.getText());
        if (editor && result) {
          editor.commands.clearContent();
          editor.commands.insertContent(result);
          aiEnhanceRef?.current?.hide();
          cursorMenuRef?.current?.hide();
          if (onChange) {
            onChange(editor.getText());
          }

          trackContentEvent('enhance', {
            enhanceType: 'full_content',
            contentLength: result.length,
          });
        }
      }
    } catch (error) {
      console.error('Error enhancing text:', error);
    } finally {
      setIsPromptRequesting(false);
    }
  }

  const handleGenerateFromTopic = async (topic: string, isKnowledge: boolean, isWebSearch: boolean) => {
    if (!editor || !onAIRequest) {
      return;
    }
    try {
      setIsPromptRequesting(true);
      const result = await onNewPostRequest(topic, isKnowledge, isWebSearch);
      if (editor && result) {
        editor.commands.clearContent();
        editor.commands.insertContent(result);
        aiEnhanceRef?.current?.hide();
        cursorMenuRef?.current?.hide();
        if (onChange) {
          onChange(editor.getText());
        }
      }
    } catch (error) {
      console.error('Error enhancing text:', error);
    } finally {
      setIsPromptRequesting(false);
    }
  }

  useEffect(() => {
    if (editor && initialContent) {
      const initialCharCount = initialContent.length;
      setCharacterCount(initialCharCount);
    }
  }, [editor, initialContent]);

  useEffect(() => {
    if (editor) {
      const currentText = editor.getText();
      if (initialContent !== currentText) {
        editor.commands.clearContent();
        if (initialContent) {
          editor.commands.insertContent(initialContent);
          const newCharacterCount = initialContent.length;
          setCharacterCount(newCharacterCount);
        } else {
          setCharacterCount(0);
        }
      } else if (initialContent && characterCount === 0) {
        setCharacterCount(initialContent.length);
      }
    }
  }, [editor, initialContent, characterCount]);

  const handleContainerClick = () => {
    if (editor && !isPromptRequesting) {
      editor.commands.focus();
      if (!editor.state.selection.content().size && !editor.isEmpty) {
        if (isMenuVisible) {
          aiEnhanceRef?.current?.hide();
        } else {
          aiEnhanceRef?.current?.show();
        }
        setIsMenuVisible(!isMenuVisible);
      }
    }
  };
  return (
    <div onClick={handleContainerClick} className={`relative mt-2 bg-white/5 backdrop-blur-sm border border-white/5 rounded-xl px-4 py-2 pr-1 ${isPromptRequesting ? "pointer-events-none" : "hover:border-violets-are-blue focus:border-violets-are-blue cursor-text"}`}>
      {isPromptRequesting && (
        <div className='bg-eerie-black/90 inset-0 absolute backdrop-blur-sm flex justify-center items-center z-10 rounded-xl'>
          <CircularSpinner />
        </div>
      )}
      <BubbleMenus
        editor={editor}
        aiEnhanceRef={aiEnhanceRef}
        aiMenuRef={aiMenuRef}
        cursorMenuRef={cursorMenuRef}
        cursorDivRef={cursorDivRef}
        stateHighlightedText={stateHighlightedText}
        setShowTopicInput={setShowTopicInput}
        showTopicInput={showTopicInput}
        isPromptRequesting={isPromptRequesting}
        newPromptValue={newPromptValue}
        setNewPromptValue={setNewPromptValue}
        handleWritePromptFocus={handleWritePromptFocus}
        handleKeyPress={handleKeyPress}
        handleEnhanceText={handleEnhanceText}
        handleGenerateFromTopic={handleGenerateFromTopic}
      />
      <div className="tiptap-editor h-auto max-h-[25vh] overflow-auto text-sm text-white">
        <EditorContent
          className="w-full h-full"
          editor={editor}
        />
      </div>
      {!isPromptRequesting && (
        <div className="mt-2">
          {attachedImages.length > 0 ? (
            <div className="flex items-end gap-2 mt-2">
              {attachedImages.map((imageUrl, index) => (
                <div key={index} className="relative w-16 h-16 rounded-xl group">
                  <Image
                    src={imageUrl}
                    width={80}
                    height={80}
                    alt={`Attached image ${index + 1}`}
                    className="object-cover w-16 h-16 rounded-xl"
                  />
                  {onImageButtonClick && (
                    <div
                      className="absolute inset-0 bg-black/60 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onImageButtonClick) {
                          onImageButtonClick();
                        }
                      }}
                    >
                      <span className="text-white text-xs font-medium">Change</span>
                    </div>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveImage()
                    }}
                    title="Remove image"
                    className="absolute -top-1 -right-1 p-0.5 bg-violets-are-blue rounded-full shadow-sm z-10"
                    type="button"
                  >
                    <X className="w-2.5 h-2.5 text-neutral-100" />
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                if (onImageButtonClick) {
                  onImageButtonClick();
                }
              }}
              title="Add media"
              className="w-16 h-16 rounded-xl border border-white/20 bg-white/5 hover:border-none hover:bg-gradient-to-tr from-han-purple to-tulip transition-colors flex items-center justify-center group"
            >
              <ImagePlus className="w-6 h-6 text-white/60 group-hover:text-white transition-colors" />
            </button>
          )}
        </div>
      )}

      {characterLimit && (
        <div className="mt-2">
          <div className="flex justify-between items-center text-xs">
            <div className={`${characterCount > characterLimit ? 'text-red-400' : 'text-gray-400'}`}>
              {characterCount}/{characterLimit} characters
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TipTapEditor;
