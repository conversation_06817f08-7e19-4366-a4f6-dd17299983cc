import type { SVGProps } from "react";

export const ChevronDownIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      fill="#7E5EF2"
      stroke="#AAAAAA"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M11.636 17.862 1.12 7.906a.387.387 0 0 1-.095-.136.332.332 0 0 1 .027-.306.423.423 0 0 1 .118-.123l1.776-1.245a.525.525 0 0 1 .166-.076.587.587 0 0 1 .372.024.483.483 0 0 1 .15.096l8.18 7.9c.023.021.051.039.083.051a.29.29 0 0 0 .204 0 .245.245 0 0 0 .084-.052l8.18-7.898a.484.484 0 0 1 .149-.097.571.571 0 0 1 .54.052l1.776 1.245a.422.422 0 0 1 .118.123.333.333 0 0 1 .027.306.388.388 0 0 1-.094.136l-10.508 9.956a.491.491 0 0 1-.166.102.578.578 0 0 1-.405 0 .49.49 0 0 1-.165-.102Z"
    />
  </svg>
);