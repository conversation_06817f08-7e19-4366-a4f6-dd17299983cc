'use client'

import React, {
  useState, useRef, useEffect, useCallback,
} from 'react';
import Konva from 'konva';
import {
  Stage, Layer,
} from 'react-konva';
import { motion } from 'framer-motion';
import { cn } from '@/common/utils/helpers';
import {
  PLATFORM_CANVAS_SIZES,
  acceptedImageMimeTypes,
  FILE_SIZE_10_MB,
} from '@/common/constants';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import toast from 'react-hot-toast';
import { CanvasSidebar } from './CanvasSidebar';
import { CanvasHeader } from './CanvasHeader';
import { FloatingToolbar } from './FloatingToolbar';
import { LayersPanel } from './LayersPanel';
import { LoadingOverlay } from './LoadingOverlay';
import { 
  CanvasLoadingProvider, useCanvasLoading,
} from './CanvasLoadingContext';

import { PictureLandscapeIcon } from '../../icons';

export const addCursorHandlers = (node: Konva.Node) => {
  if (!node.draggable()) {
    return;
  }
  node.on('mouseover', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'move';
    }
  });

  node.on('mouseout', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'default';
    }
  });

  node.on('dragstart', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'grabbing';
    }

    const clickedNode = e.target;
    if (clickedNode.getClassName() === 'Transformer') {
      return;
    }

    const layer = clickedNode.getLayer();
    if (!layer) {
      return;
    }
    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }
    transformer.nodes([clickedNode]);
    stage?.batchDraw();
  });

  node.on('dragend', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'move';
    }
  });
};

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
  platform?: string;
}

const CanvasEditorContent = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
  platform,
}: CanvasEditorProps) => {
  const stageRef = useRef<Konva.Stage>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const [konvaStage, setKonvaStage] = useState<Konva.Stage | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isManualZoom, setIsManualZoom] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showLayers, setShowLayers] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [backgroundRect, setBackgroundRect] = useState<Konva.Rect | null>(null);
  const {
    loadingStates, setSaving,
  } = useCanvasLoading();
  const [stageSize, setStageSize] = useState<{ width: number; height: number }>(() => {
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    return {
      width: canvasSize.width,
      height: canvasSize.height,
    };
  });

  const { activeProject } = useProjectContext();

  const createBackgroundRect = useCallback((stage: Konva.Stage, layer: Konva.Layer) => {
    const canvasWidth = stage.width();
    const canvasHeight = stage.height();

    const rect = new Konva.Rect({
      x: 0,
      y: 0,
      width: canvasWidth,
      height: canvasHeight,
      fill: backgroundColor,
      name: 'background-rect',
      listening: false,
    });

    layer.add(rect);
    rect.moveToBottom();
    setBackgroundRect(rect);

    return rect;
  }, [backgroundColor]);

  const updateBackgroundColor = useCallback((newColor: string) => {
    setBackgroundColor(newColor);
    if (backgroundRect) {
      backgroundRect.fill(newColor);
      backgroundRect.getLayer()?.batchDraw();
    }
  }, [backgroundRect]);

  const updateStageSize = useCallback(() => {
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;

    setStageSize({
      width: canvasSize.width,
      height: canvasSize.height,
    });

    if (konvaStage) {
      konvaStage.width(canvasSize.width);
      konvaStage.height(canvasSize.height);
      konvaStage.batchDraw();
    }
  }, [platform, konvaStage]);

  const handleZoomChange = useCallback((newZoom: number) => {
    setIsManualZoom(true);
    setZoomLevel(newZoom);
  }, []);

  const fitToView = useCallback(() => {
    if (!canvasContainerRef.current) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const sceneWidth = canvasSize.width;
    const sceneHeight = canvasSize.height;

    const container = canvasContainerRef.current;
    const containerWidth = container.clientWidth - 80;
    const containerHeight = container.clientHeight - 80;

    const scaleX = containerWidth / sceneWidth;
    const scaleY = containerHeight / sceneHeight;
    const newZoom = Math.min(scaleX, scaleY, 1.25);

    setIsManualZoom(false);
    setZoomLevel(newZoom);
  }, [platform]);

  const validateFile = useCallback((file: File): string | null => {
    if (!acceptedImageMimeTypes.includes(file.type)) {
      return 'Please upload a valid image file (JPEG, PNG, GIF, SVG, or WebP)';
    }

    if (file.size > FILE_SIZE_10_MB) {
      return 'File size must be less than 10MB';
    }

    return null;
  }, []);

  const scaleImageToFitCanvas = useCallback((
    img: Konva.Image,
    canvas: Konva.Stage,
  ) => {
    let layer = canvas.findOne('Layer') as Konva.Layer;
    if (!layer) {
      layer = new Konva.Layer();
      canvas.add(layer);
    }

    const canvasWidth = canvas.width();
    const canvasHeight = canvas.height();
    const imageElement = img.image() as HTMLImageElement;
    const imgWidth = imageElement?.width || 1;
    const imgHeight = imageElement?.height || 1;

    const scaleX = canvasWidth / imgWidth;
    const scaleY = canvasHeight / imgHeight;
    const scale = Math.min(scaleX, scaleY);

    img.x((canvasWidth - imgWidth * scale) / 2);
    img.y((canvasHeight - imgHeight * scale) / 2);
    img.scaleX(scale);
    img.scaleY(scale);
    img.draggable(true);

    addCursorHandlers(img);

    layer.add(img);
    img.moveToTop();

    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }

    transformer.nodes([img]);
    canvas.batchDraw();
  }, []);

  const handleFileUpload = useCallback(async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    if (!konvaStage) {
      toast.error('Canvas not ready');
      return;
    }

    try {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imgUrl = event.target?.result as string;
        const imageObj = new window.Image();
        imageObj.onload = () => {
          const konvaImage = new Konva.Image({
            image: imageObj,
          });
          scaleImageToFitCanvas(konvaImage, konvaStage);

          if (activeProject?.project_id && agentId) {
            projectImageStorage.addUploadedImage(
              activeProject.project_id,
              agentId,
              imgUrl,
              file.name,
              planId,
            ).then(() => {
              window.dispatchEvent(new CustomEvent('projectImagesUpdated', {
                detail: { projectId: activeProject.project_id },
              }));
            }).catch((error) => {
              console.error('Error storing uploaded image:', error);
            });
          }

          toast.success('Image added to canvas!');
        };
        imageObj.src = imgUrl;
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    }
  }, [konvaStage, validateFile, scaleImageToFitCanvas, activeProject, agentId, planId]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!canvasContainerRef.current?.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => acceptedImageMimeTypes.includes(file.type));

    if (imageFiles.length === 0) {
      toast.error('Please drop valid image files (JPEG, PNG, GIF, SVG, or WebP)');
      return;
    }

    if (imageFiles.length > 0) {
      handleFileUpload(imageFiles[0]);
    }

    if (imageFiles.length > 1) {
      toast.success(`Added first image. ${imageFiles.length - 1} other images were ignored.`);
    }
  }, [handleFileUpload]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }
    const handleResize = () => {
      updateStageSize();
    };

    updateStageSize();

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen, updateStageSize]);

  useEffect(() => {
    if (!stageRef.current || !isOpen || isInitialized) {
      return;
    }

    const initializeStage = () => {
      const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
      const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
      const canvasWidth = canvasSize.width;
      const canvasHeight = canvasSize.height;

      const stage = stageRef.current!;
      stage.width(canvasWidth);
      stage.height(canvasHeight);

      let layer = stage.findOne('Layer') as Konva.Layer;
      if (!layer) {
        layer = new Konva.Layer();
        stage.add(layer);
      }

      createBackgroundRect(stage, layer);

      stage.batchDraw();

      setTimeout(() => {
        setKonvaStage(stage);
        setIsInitialized(true);
      }, 50);

      setTimeout(() => {
        if (canvasContainerRef.current) {
          fitToView();
          const container = canvasContainerRef.current;
          const scrollLeft = (container.scrollWidth - container.clientWidth) / 2;
          const scrollTop = (container.scrollHeight - container.clientHeight) / 2;
          container.scrollTo({
            left: scrollLeft,
            top: scrollTop,
            behavior: 'smooth',
          });
        }
      }, 200);

      if (initialImage) {
        const imageObj = new Image();
        imageObj.crossOrigin = 'anonymous';
        imageObj.onload = () => {
          const currentLayer = stage.findOne('Layer') as Konva.Layer;
          if (!currentLayer) {
            return;
          }

          const canvasWidth = stage.width();
          const canvasHeight = stage.height();
          const imgWidth = imageObj.width;
          const imgHeight = imageObj.height;

          const scaleX = canvasWidth / imgWidth;
          const scaleY = canvasHeight / imgHeight;
          const scale = Math.min(scaleX, scaleY);

          const konvaImage = new Konva.Image({
            image: imageObj,
            x: (canvasWidth - imgWidth * scale) / 2,
            y: (canvasHeight - imgHeight * scale) / 2,
            scaleX: scale,
            scaleY: scale,
            draggable: true,
          });

          addCursorHandlers(konvaImage);

          currentLayer.add(konvaImage);
          konvaImage.moveToTop();
          currentLayer.batchDraw();
        };
        imageObj.src = initialImage;
      }
    };

    initializeStage();
  }, [isOpen, initialImage, platform, fitToView, isInitialized, createBackgroundRect]);
  useEffect(() => {
    if (!isOpen && isInitialized) {
      setIsInitialized(false);
      setKonvaStage(null);
      setZoomLevel(1);
      setIsManualZoom(false);
      setShowLayers(false);
      setBackgroundRect(null);
      setBackgroundColor('#ffffff');
    }
  }, [isOpen, isInitialized]);

  const deleteSelectedObjects = useCallback(() => {
    if (!konvaStage) {
      return;
    }

    const transformer = konvaStage.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      return;
    }

    const selectedNodes = transformer.nodes();
    if (selectedNodes.length === 0) {
      return;
    }

    selectedNodes.forEach((node) => {
      if (node.name() !== 'background-rect') {
        node.destroy();
      }
    });

    transformer.nodes([]);
    konvaStage.batchDraw();
  }, [konvaStage]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        const target = e.target as HTMLElement;
        if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA' && !target.isContentEditable) {
          e.preventDefault();
          deleteSelectedObjects();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, deleteSelectedObjects]);

  const handleSaveDesign = async () => {
    if (!konvaStage) {
      console.error('Canvas not available');
      return;
    }

    if (!activeProject?.project_id) {
      console.error('No active project');
      toast.error('No active project found');
      return;
    }

    setSaving(true);
    try {
      const dataURL = konvaStage.toDataURL({
        mimeType: 'image/png',
        quality: 1,
        pixelRatio: 1,
      });

      const response = await fetch(dataURL);
      const blob = await response.blob();
      const timestamp = Date.now();
      const fileName = `canvas-design-${timestamp}.png`;
      const file = new File([blob], fileName, { type: 'image/png' });

      const formData = new FormData();
      formData.append('image', file);
      formData.append('planId', planId || 'canvas-save');

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const uploadEndpoint = `${baseUrl}/agents/${agentId}/upload-canvas-image`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload canvas image');
      }

      const uploadResult = await uploadResponse.json();

      if (uploadResult.success && uploadResult.filepath) {
        await projectImageStorage.addCreationImage(
          activeProject.project_id,
          agentId,
          uploadResult.filepath,
          fileName,
          planId,
          'Canvas Design',
        );

        window.dispatchEvent(new CustomEvent('projectImagesUpdated', {
          detail: { projectId: activeProject.project_id },
        }));

        onSave(uploadResult.filepath);
        toast.success('Canvas design saved successfully!');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error saving canvas design:', error);
      toast.error('Failed to save canvas design');
      onSave('');
    } finally {
      setSaving(false);
    }
  };

  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = stageRef.current;
    if (!stage) {
      return;
    }

    if (e.target === stage) {
      const transformer = stage.findOne('Transformer') as Konva.Transformer;
      if (transformer) {
        transformer.nodes([]);
        stage.batchDraw();
      }
      return;
    }

    const clickedNode = e.target;
    if (clickedNode.getClassName() === 'Transformer') {
      return;
    }

    if (clickedNode.name() === 'background-rect') {
      return;
    }

    const layer = clickedNode.getLayer();
    if (!layer) {
      return;
    }

    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }

    transformer.nodes([clickedNode]);
    stage.batchDraw();
  };

  useEffect(() => {
    if (!konvaStage || !isInitialized) {
      return;
    }

    const layers = konvaStage.getLayers();
    if (layers.length === 0) {
      const newLayer = new Konva.Layer();
      konvaStage.add(newLayer);
      konvaStage.batchDraw();
    }
  }, [konvaStage, isInitialized]);

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-0 left-0 right-0 bottom-0 h-[calc(100vh)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasHeader
        onSaveDesign={handleSaveDesign}
      />

      <motion.div
        className="flex flex-1 min-h-0 flex-col md:flex-row"
        initial={{ opacity: 0  }}
        animate={{ opacity: 1 }}
        transition={{ 
          duration: 0.6, 
          delay: 1.2,
        }}
      >
        <motion.div
          className="block"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            duration: 0.5,
            delay: 1.4,
          }}
        >
          {konvaStage && isInitialized && (
            <CanvasSidebar
              canvas={konvaStage}
              agentId={agentId}
              planId={planId}
              containerRef={canvasContainerRef}
              zoomLevel={zoomLevel}
              onClose={onClose}
              loadingStates={loadingStates}
            />
          )}
        </motion.div>
        <motion.div
          className="flex-1 bg-neutral-800 flex flex-col"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ 
            duration: 0.6, 
            delay: 1.6,
          }}
        >
          <div
            ref={canvasContainerRef}
            className={cn(
              "flex-1 w-full h-full overflow-auto scroll-smooth relative",
              isDragOver && "bg-violets-are-blue/10",
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div
              className="flex items-center justify-center"
              style={{
                minHeight: `calc(100% + ${Math.max(200, stageSize.height * zoomLevel * 0.5)}px)`,
                minWidth: `calc(100% + ${Math.max(200, stageSize.width * zoomLevel * 0.5)}px)`,
                padding: '100px',
              }}
            >
              <div
                style={{
                  transform: `scale(${zoomLevel})`,
                  transformOrigin: 'center',
                  transition: isManualZoom ? 'none' : 'transform 0.2s ease-out relative',
                }}
              >
                <Stage
                  ref={stageRef}
                  onClick={handleStageClick}
                  width={stageSize.width}
                  height={stageSize.height}
                  className="block overflow-hidden bg-white "
                >
                  <Layer />
                </Stage>
                {isDragOver && (
                  <div className="absolute inset-0 bg-gradient-to-tr from-han-purple to-tulip flex items-center justify-center z-50">
                    <div className="bg-neutral-900/90 rounded-3xl p-6 text-center">
                      <div className="text-2xl mb-2 flex justify-center"><PictureLandscapeIcon /></div>
                      <p className="text-white font-medium">Drop image here</p>
                      <p className="text-gray-400 text-sm">PNG, JPG, SVG, GIF, WebP up to 10MB</p>
                    </div>
                  </div>
                )}
                <LoadingOverlay
                  isVisible={loadingStates.isSaving || loadingStates.isGenerating || loadingStates.isVectorizing || loadingStates.isImproving}
                  type={
                    loadingStates.isSaving ? "saving" :
                      loadingStates.isGenerating ? "generating" :
                        loadingStates.isVectorizing ? "vectorizing" : "improving"
                  }
                  message={
                    loadingStates.isSaving ? "Saving your design..." :
                      loadingStates.isGenerating ? "Creating your image with AI..." :
                        loadingStates.isVectorizing ? "Generating vector image..." :
                          loadingStates.isImproving ? "Enhancing your image with AI..." : "Processing..."
                  }
                />
              </div>
            </div>
            {showLayers && (
              <LayersPanel
                canvas={konvaStage}
                onClose={() => setShowLayers(false)}
              />
            )}
          </div>
          <motion.div
            className="text-center text-gray-500 text-xs"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.4,
              delay: 2.0,
            }}
          >
            <div className="flex flex-col md:flex-row items-center justify-between gap-2 md:gap-4">
              {/* <p className="text-center md:text-left">Select layer for more options | Double-click text to edit inline | Delete key to remove selected objects</p> */}
              <div></div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{
                  duration: 0.5,
                  delay: 1.8,
                }}
              >
                <FloatingToolbar
                  canvas={konvaStage}
                  zoomLevel={zoomLevel}
                  onZoomChange={handleZoomChange}
                  onFitToView={fitToView}
                  showLayers={showLayers}
                  onToggleLayers={() => setShowLayers(!showLayers)}
                  backgroundColor={backgroundColor}
                  onBackgroundColorChange={updateBackgroundColor}
                />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export const CanvasEditor = (props: CanvasEditorProps) => {
  return (
    <CanvasLoadingProvider>
      <CanvasEditorContent {...props} />
    </CanvasLoadingProvider>
  );
};
