'use client'

import React, {
  useState, useEffect, useRef,
} from 'react';
import Konva from 'konva';
import {
  ZoomIn,
  ZoomOut,
  Layers,
  HelpCircle,
  Palette,
} from 'lucide-react';
import { secondaryFont } from '@/common/utils/localFont';
import { HelpModal } from './HelpModal';

interface FloatingToolbarProps {
  canvas: Konva.Stage | null;
  zoomLevel: number;
  onZoomChange: (zoom: number) => void;
  onFitToView: () => void;
  showLayers?: boolean;
  onToggleLayers?: () => void;
  backgroundColor?: string;
  onBackgroundColorChange?: (color: string) => void;
}

export const FloatingToolbar = ({
  zoomLevel,
  onZoomChange,
  onFitToView,
  showLayers = false,
  onToggleLayers,
  backgroundColor = '#ffffff',
  onBackgroundColorChange,
}: FloatingToolbarProps) => {
  const [showHelp, setShowHelp] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const colorPickerRef = useRef<HTMLDivElement>(null);

  const handleZoomIn = () => {
    const newZoom = Math.min(zoomLevel * 1.2, 1.25);
    onZoomChange(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoomLevel / 1.2, 0.1);
    onZoomChange(newZoom);
  };

  const zoomPercentage = Math.round(zoomLevel * 100);

  const predefinedColors = [
    '#ffffff', // White
    '#f3f4f6', // Light gray
    '#e5e7eb', // Gray
    '#000000', // Black
    '#1f2937', // Dark gray
    '#ef4444', // Red
    '#f97316', // Orange
    '#eab308', // Yellow
    '#22c55e', // Green
    '#3b82f6', // Blue
    '#8b5cf6', // Purple
    '#ec4899', // Pink
  ];

  const handleColorChange = (color: string) => {
    if (onBackgroundColorChange) {
      onBackgroundColorChange(color);
    }
    setShowColorPicker(false);
  };

  // Close color picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node)) {
        setShowColorPicker(false);
      }
    };

    if (showColorPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showColorPicker]);

  return (
    <>
      <div className="flex items-center justify-center gap-1 rounded-lg p-1">
        <button
          onClick={handleZoomOut}
          className="p-2 rounded-lg hover:bg-neutral-600 transition-colors"
          title="Zoom Out"
        >
          <ZoomOut size={16} className="text-gray-300" />
        </button>
        <button
          onClick={onFitToView}
          className={`px-3 py-1 text-sm font-medium text-gray-300 hover:bg-neutral-600 rounded-lg transition-colors min-w-[60px] ${secondaryFont.className}`}
          title="Fit to View"
        >
          {zoomPercentage}%
        </button>
        <button
          onClick={handleZoomIn}
          className="p-2 rounded-lg hover:bg-neutral-600 transition-colors"
          title="Zoom In"
        >
          <ZoomIn size={16} className="text-gray-300" />
        </button>
        <div className="w-px h-6 bg-neutral-500 mx-1" />
        <button
          onClick={onToggleLayers}
          className={`p-2 rounded-lg transition-colors ${
            showLayers ? 'bg-violets-are-blue text-white' : 'hover:bg-neutral-600 text-gray-300'
          }`}
          title="Layers"
        >
          <Layers size={16} />
        </button>
        <div className="relative" ref={colorPickerRef}>
          <button
            onClick={() => setShowColorPicker(!showColorPicker)}
            className="p-2 rounded-lg hover:bg-neutral-600 transition-colors relative"
            title="Background Color"
          >
            <Palette size={16} className="text-gray-300" />
            <div
              className="absolute bottom-0 right-0 w-3 h-3 rounded-full border border-neutral-400"
              style={{ backgroundColor }}
            />
          </button>
          {showColorPicker && (
            <div className="absolute bottom-full mb-2 right-0 bg-neutral-800 border border-neutral-600 rounded-lg p-3 shadow-lg">
              <div className="grid grid-cols-4 gap-2 w-32">
                {predefinedColors.map((color) => (
                  <button
                    key={color}
                    onClick={() => handleColorChange(color)}
                    className={`w-6 h-6 rounded border-2 transition-all ${
                      backgroundColor === color
                        ? 'border-white scale-110'
                        : 'border-neutral-500 hover:border-neutral-400'
                    }`}
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
        <button
          onClick={() => setShowHelp(true)}
          className="p-2 rounded-lg hover:bg-neutral-600 transition-colors"
          title="Help"
        >
          <HelpCircle size={16} className="text-gray-300" />
        </button>
      </div>

      <HelpModal
        isOpen={showHelp}
        onClose={() => setShowHelp(false)}
      />
    </>
  );
};
